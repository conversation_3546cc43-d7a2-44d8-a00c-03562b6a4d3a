
/**@file  	   device_manage.hpp
* @brief       本播墙调度系统的地图管理源代码
* @details     NULL
* <AUTHOR>
* @date        2024-10-10
* @version     v1.0.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.1.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2024/10/10  <td>1.0.0    <td>lizhy     <td>初始版本	                   </tr>
* </table>
*
**********************************************************************************
*/




#ifndef __CAMERA_MANAGER_HPP__
#define __CAMERA_MANAGER_HPP__

#include "../threadpool/blocking_queue.hpp"
#include "../setting/setting.hpp"
#include "../net/udp_socket.hpp"

#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"

#include "share/pb/idl/data_map.pb.h"
#include "share/pb/idl/data_request.pb.h"
#include "share/pb/idl/train_interface.pb.h"
#include "share/pb/idl/feeder_interface.pb.h"
#include "share/pb/idl/container_interface.pb.h"

#include <string>
#include <iostream>
#include <vector>
#include <map>
#include <functional>
#include <memory>
#include <unordered_map>
#include <list>
#include <zmq.h>
#include <cppzmq/zmq.hpp>

#include <thread>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <errno.h>
#include <stdexcept>
#include <mutex> 
#include <unordered_map>


using namespace std;


typedef struct __camera_msg
{
	int id;
	bool result;
	float area;
}camera_msg;


/**
* @brief 根据JSON文件生成地图信息
*/
class camera_manager
{
public:
	
	
	~camera_manager();

	bool camera_manager_init(zmq::context_t &ctx);
	void camera_manager_run(); 

	void camera_manager_camera_data_ctrl_thread(void);

	int camera_manager_camera_id_match(struct sockaddr_in  dev_addr);

	camera_msg camera_manager_camera_msg_pop(void);

	void camera_manager_bs_camera_data_ctrl_thread(void);


	static camera_manager *get_instance(void)
    {
        static camera_manager instance;
        return &instance;
    }
	
private:

	//std::mutex m_map_apply_lock;

	zmq::socket_t *m_slot_state_sub;

	zmq::socket_t *m_seal_state_sub;

	udp_server_socket m_camera_comm_server;

	gy_camera_cfg m_sys_camera_cfg;

	std::thread *m_camera_ctrl_thread;
	std::thread *m_bs_camera_ctrl_thread;

	int m_camera_server_fd;

	blocking_queue<camera_msg> m_carmear_msg_queue;

	bool m_hk_net_init_flag;
	bool m_bx_raw_init_flag;

};





#endif
